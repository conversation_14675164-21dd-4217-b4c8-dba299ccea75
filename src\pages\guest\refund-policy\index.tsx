import { Separator } from '@/components/ui/separator';
import React from 'react'

const RefundPolicy: React.FC = () => {
  return (
    <div className='min-h-screen w-full flex flex-col'>
      <div className='max-w-6xl mx-auto my-10 w-full'>
        <h1 className='text-4xl font-bold text-black'>Refund & Returns Policy</h1>
      </div>
      <Separator className='bg-brand-dark max-w-6xl mx-auto' />

      <div className='max-w-5xl mx-auto my-10 space-y-8 text-black flex-grow'>
        <div className='space-y-4'>
          <p className='text-3xl font-semibold'>Welcome to Klout Club!</p>
          <p className='font-semibold text-lg'>Our refund and returns policy lasts 30 days. If 30 days have passed since your purchase, we can't offer you a full refund or exchange.</p>
          <p>To be eligible for a return, your item must be unused and in the same condition that you received it. It must also be in the original packaging.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Non-Returnable Items</h2>
          <p>Several types of goods are exempt from being returned. Perishable goods such as food, flowers, newspapers, or magazines cannot be returned. We also do not accept products that are intimate or sanitary goods, hazardous materials, or flammable liquids or gases.</p>
          
          <p className='font-medium'>Additional non-returnable items:</p>
          <ul className='list-disc pl-6 space-y-2'>
            <li>Gift cards</li>
            <li>Downloadable software products</li>
            <li>Some health and personal care items</li>
          </ul>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Return Requirements</h2>
          <p>To complete your return, we require a receipt or proof of purchase.</p>
          <p>Please do not send your purchase back to the manufacturer.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Partial Refunds</h2>
          <p>There are certain situations where only partial refunds are granted:</p>
          <ul className='list-disc pl-6 space-y-2'>
            <li>Books with obvious signs of use</li>
            <li>CDs, DVDs, VHS tapes, software, video games, cassette tapes, or vinyl records that have been opened</li>
            <li>Any item not in its original condition, is damaged, or missing parts for reasons not due to our error</li>
            <li>Any item that is returned more than 30 days after delivery</li>
          </ul>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Refunds</h2>
          <p>Once your return is received and inspected, we will send you an email to notify you that we have received your returned item. We will also notify you of the approval or rejection of your refund.</p>
          <p>If you are approved, then your refund will be processed, and a credit will automatically be applied to your credit card or original method of payment, within a certain amount of days.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Late or Missing Refunds</h2>
          <p>If you haven't received a refund yet, first check your bank account again.</p>
          <ul className='list-disc pl-6 space-y-2'>
            <li>Then contact your credit card company; it may take some time before your refund is officially posted.</li>
            <li>Next, contact your bank. There is often some processing time before a refund is posted.</li>
            <li>If you've done all of this and you still have not received your refund yet, please contact us at <span className="text-brand-primary"><EMAIL></span></li>
          </ul>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Sale Items</h2>
          <p>Only regular-priced items may be refunded. Sale items cannot be refunded.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Exchanges</h2>
          <p>We only replace items if they are defective or damaged. If you need to exchange it for the same item, send us an email at <span className="text-brand-primary"><EMAIL></span> and send your item to:</p>
          <p className='font-medium'>618-P, OHFEO Workplace, Durga colony, Jharsa Village, Sector-39, Gurugram, Haryana 122003</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Gifts</h2>
          <p>If the item was marked as a gift when purchased and shipped directly to you, you'll receive a gift credit for the value of your return. Once the returned item is received, a gift certificate will be mailed to you.</p>
          <p>If the item wasn't marked as a gift when purchased, or the gift giver had the order shipped to themselves to give to you later, we will send a refund to the gift giver, and they will find out about your return.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Shipping Returns</h2>
          <p>To return your product, you should mail your product to:</p>
          <p className='font-medium'>618-P, OHFEO Workplace, Durga colony, Jharsa Village, Sector-39, Gurugram, Haryana 122003</p>
          <p>You will be responsible for paying for your own shipping costs for returning your item. Shipping costs are non-refundable. If you receive a refund, the cost of return shipping will be deducted from your refund.</p>
          <p>Depending on where you live, the time it may take for your exchanged product to reach you may vary.</p>
          <p>If you are returning more expensive items, you may consider using a trackable shipping service or purchasing shipping insurance. We don't guarantee that we will receive your returned item.</p>
        </div>

        <div className='space-y-4'>
          <h2 className='text-3xl font-semibold'>Need Help?</h2>
          <p>Contact us at <span className="text-brand-primary"><EMAIL></span> for questions related to refunds and returns.</p>
        </div>
      </div>
    </div>
  )
}

export default RefundPolicy; 