/* SF Pro Text Font Family */
@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Heavy.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('./assets/fonts/sf-pro/SFProText-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

/* Date Stamp Font */
@font-face {
  font-family: 'Date Stamp';
  src: url('./assets/fonts/date-stamp/date-stamp-regular.otf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Date Stamp';
  src: url('./assets/fonts/date-stamp/date-stamp-bold.otf') format('truetype');
  font-weight: 400;
  font-style: normal;
}